package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.service.IAmsGlamsService;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherAbsMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherAbs;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsPageDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

/**
 * @description: 会计流水操作
 * @author: ZXL
 * @create: 2019-10-10 11:14
 */
@Service
public class AmsGlamsServiceImpl implements IAmsGlamsService {
    private static final Logger logger = LoggerFactory.getLogger(AmsGlamsServiceImpl.class);
    @Resource
    private AccountantGlamsSelfMapper tAmsGlamsSelfMapper;
    @Resource
    private AccountantGlvcherSelfMapper tAmsGlvcherSelfMapper;
    @Resource
    private AccountantGlvcherMapper tAmsGlvcherMapper;
    @Resource
    private AccountantGlvcherAbsMapper tAmsGlvcherAbsMapper;
    @Resource
    private AccountantGlamsMapper tAmsGlamsMapper;
    @Resource
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;
    @Resource
    private ITPmsGlamsDefinitionService glamsDefinitionService;
    @Resource
    private ITPmsGlacgnService glacgnService;
    @Resource
    private ITransactionTypeService transactionTypeService;
    @Resource
    private ITransactionCodeService transactionCodeService;


    @Override
    public int getAccountManageIdCount(String partitionKey) {
        logger.info("Get account manage id count: partitionKey={}", partitionKey);
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return tAmsGlamsSelfMapper.getAccountManageCount(OrgNumberUtils.getOrg(),partitionKey0, partitionKey1);
    }

    @Override
    public List<String> queryAccountManageIds(String partitionKey, List<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return tAmsGlamsSelfMapper.selectAccountManageIds(OrgNumberUtils.getOrg(),partitionKey0, partitionKey1, rowNumbers);
    }

    @Override
    public TAmsGlamsPageDTO findTamsGlamsById(String tAmsGlamsId) {
        AccountantGlams tAmsGlams = tAmsGlamsMapper.selectByPrimaryKey(tAmsGlamsId);
        TAmsGlamsPageDTO tAmsGlamsPageDTO = new TAmsGlamsPageDTO();
        if (tAmsGlams != null) {
            TransactionTypeResDTO transactionType = null;
            TransactionCodeResDTO transactionCode=null;
            TransactionCodeResDTO transactionCodeOrig=null;
            ParmAcctProductMainInfo acctProductMainInfo = null;
            TPmsGlamsDefinitionDTO pmsGlamsDefinition=null;
            try {
                //交易类型描述
                if (tAmsGlams.getTransactionTypeCode() != null) {
                    transactionType = transactionTypeService.findTransactionType(tAmsGlams.getOrganizationNumber(),
                            tAmsGlams.getTransactionTypeCode());
                }}
            catch (Exception e){
                logger.error("Current parameter is not defined: transactionTypeCode={}", tAmsGlams.getTransactionTypeCode(), e);
            }
            try {
                //交易码描述
                if (tAmsGlams.getTxnCode() != null) {
                    transactionCode = transactionCodeService.findTransactionCode(tAmsGlams.getOrganizationNumber(),
                            tAmsGlams.getTxnCode());
                }}
            catch (Exception e){
                logger.error("Current parameter is not defined: txnCode={}", tAmsGlams.getTxnCode(), e);
            }
            try {
                //原始交易码描述
                if (tAmsGlams.getTxnCodeOrig() != null) {
                    transactionCodeOrig = transactionCodeService.findTransactionCode(tAmsGlams.getOrganizationNumber(),
                            tAmsGlams.getTxnCodeOrig());
                }}
            catch (Exception e){
                logger.error("Current parameter is not defined: txnCodeOrig={}", tAmsGlams.getTxnCodeOrig(), e);
            }
            try {
                //原始交易码描述
                if (tAmsGlams.getAcctLogo() != null) {
                    acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(tAmsGlams.getOrganizationNumber(),tAmsGlams.getAcctLogo());


                }
            }
            catch (Exception e){
                logger.error("Current parameter is not defined: acctLogo={}", tAmsGlams.getAcctLogo(), e);
            }
            try {
                //流水控制表
                if (tAmsGlams.getRuleId() != null) {
                    pmsGlamsDefinition = glamsDefinitionService.selectByIndex(tAmsGlams.getOrganizationNumber(),
                            "110110",tAmsGlams.getRuleId());
                }
            }catch (Exception e){
                logger.error("Current parameter is not defined: ruleId={}", tAmsGlams.getRuleId(), e);
            }
            tAmsGlamsPageDTO=BeanMapping.copy(tAmsGlams, TAmsGlamsPageDTO.class);

            if (transactionType != null) {
                tAmsGlamsPageDTO.setTransactionTypeCodeDesc(transactionType.getDescription());
            }
            if (transactionCode != null) {
                tAmsGlamsPageDTO.setTxnCodeDesc(transactionCode.getDescription());
            }
            if (transactionCodeOrig != null) {
                tAmsGlamsPageDTO.setTxnCodeOrigDesc(transactionCodeOrig.getDescription());
            }
            if (pmsGlamsDefinition != null) {
                tAmsGlamsPageDTO.setRuleIdDesc(tAmsGlams.getRuleId()+" - "+pmsGlamsDefinition.getDescription());
            }
            if (acctProductMainInfo != null) {
                tAmsGlamsPageDTO.setAcctLogoDesc(acctProductMainInfo.getDescription());
            }
        }
        return tAmsGlamsPageDTO;
    }

    @Override
    public TAmsGlvcherDTO findtAmsGlvcherById(String tAmsGlvcherId, String tableSource) {
        AccountantGlvcher tAmsGlvcher = null;
        AccountantGlvcherAbs tAmsGlvcherAbs = null;
        TAmsGlvcherDTO tAmsGlvcherDTO=null;
        if (StringUtils.isNotBlank(tableSource) && "1".equals(tableSource)) {
            tAmsGlvcher = tAmsGlvcherMapper.selectByPrimaryKey(tAmsGlvcherId);
        } else {
            tAmsGlvcherAbs = tAmsGlvcherAbsMapper.selectByPrimaryKey(tAmsGlvcherId);
            if (tAmsGlvcherAbs != null) {
                tAmsGlvcher=new AccountantGlvcher();
                BeanUtils.copyProperties(tAmsGlvcherAbs, tAmsGlvcher);
            }
        }
        if (tAmsGlvcher != null) {
            TPmsGlacgnDTO byGlAcctAndCurrCode =
                    glacgnService.findByGlAcctAndCurrCode(tAmsGlvcher.getOrganizationNumber(),
                            tAmsGlvcher.getBranchid(), tAmsGlvcher.getGlAcct(), tAmsGlvcher.getCurrCode());
            if (byGlAcctAndCurrCode == null) {
                tAmsGlvcher.setGlAcctName(String.format("Subject does not exist in subject parameter table:[%s][%s]", tAmsGlvcher.getGlAcct(),
                        tAmsGlvcher.getCurrCode()));
            }else {
                tAmsGlvcher.setGlAcctName(byGlAcctAndCurrCode.getGlAcctName());
            }
            TransactionCodeResDTO transactionCode=null;
            TransactionCodeResDTO transactionCodeOrig=null;
            ParmAcctProductMainInfo acctProductMainInfo = null;
            try {
                //交易码描述
                if (tAmsGlvcher.getVpTxnCode() != null) {
                    transactionCode = transactionCodeService.findTransactionCode(tAmsGlvcher.getOrganizationNumber(),
                            tAmsGlvcher.getVpTxnCode());
                }
            }catch (Exception e){
                logger.error("Current parameter is not defined: vpTxnCode={}", tAmsGlvcher.getVpTxnCode(), e);
            }
            try {
                //原始交易码描述
                if (tAmsGlvcher.getTxnCodeOrig() != null) {
                    transactionCodeOrig = transactionCodeService.findTransactionCode(tAmsGlvcher.getOrganizationNumber(),
                            tAmsGlvcher.getTxnCodeOrig());
                } }catch (Exception e){
                logger.error("Current parameter is not defined: txnCodeOrig={}", tAmsGlvcher.getTxnCodeOrig(), e);
            }
            try {
                //原始交易码描述
                if (tAmsGlvcher.getAcctLogo() != null) {
                    acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(tAmsGlvcher.getOrganizationNumber(),tAmsGlvcher.getAcctLogo());

                }
            }catch (Exception e){
                logger.error("Current parameter is not defined: acctLogo={}", tAmsGlvcher.getAcctLogo(), e);
            }
            tAmsGlvcherDTO = BeanMapping.copy(tAmsGlvcher, TAmsGlvcherDTO.class);

            if (transactionCode != null) {
                tAmsGlvcherDTO.setVpTxnCodeDesc(transactionCode.getDescription());
            }
            if (transactionCodeOrig != null) {
                tAmsGlvcherDTO.setTxnCodeOrigDesc(transactionCodeOrig.getDescription());
            }
            if (acctProductMainInfo != null) {
                tAmsGlvcherDTO.setAcctLogoDesc(acctProductMainInfo.getDescription());
            }
        }
        return tAmsGlvcherDTO;
    }

    @Override
    public PageResultDTO<TAmsGlamsPageDTO> getTamsGlamsByPage(Integer page, Integer rows, String postingDate,
                                                              String accountManagementId, String globalFlowNo) {
        Page page1 = PageHelper.startPage(page, rows);
        List<AccountantGlams> tAmsGlamsByPage = tAmsGlamsSelfMapper.getTamsGlamsByPage(OrgNumberUtils.getOrg(),postingDate, accountManagementId,
                globalFlowNo);
        List<TAmsGlamsPageDTO> tAmsGlamsPageDtoS = BeanMapping.copyList(tAmsGlamsByPage, TAmsGlamsPageDTO.class);
        if (CollectionUtils.isNotEmpty(tAmsGlamsPageDtoS)) {
            tAmsGlamsPageDtoS.forEach(o -> {
                TransactionCodeResDTO transactionCode=null;
                if (o.getTxnCode() != null) {
                    try {
                        transactionCode = transactionCodeService.findTransactionCode(o.getOrganizationNumber(),
                                o.getTxnCode());
                    }catch (Exception e){
                        logger.error("Current parameter is not defined: txnCode={}", o.getTxnCode(), e);
                    }
                }
                if (transactionCode != null) {
                    o.setTxnCodeDesc(transactionCode.getDescription());
                }
            });
        }
        return   new PageResultDTO(page, rows, page1.getTotal(),page1.getPages(),tAmsGlamsPageDtoS);
    }

    @Override
    public PageResultDTO<TAmsGlvcherDTO> getTamsGlvcherPage(Integer page, Integer rows, String accountManagementId,
                                                            String globalFlowNo, String postingDate,
                                                            String tableSource, String processType) {
        Page page1 = PageHelper.startPage(page, rows);
        LocalDate date = null;
        if (StringUtils.isNotBlank(postingDate)) {
            date = DateHelper.toLocalDate(postingDate);
        }

        HashMap<String, Object> map = new HashMap<>(8);
        map.put("managementId",accountManagementId);
        map.put("globalFlowNo",globalFlowNo);
        map.put("postingDate",date);
        map.put("tableSource",tableSource);
        map.put("organizationNumber", OrgNumberUtils.getOrg());
        String tableName="ACCOUNTANT_GLVCHER";
        if ("2".equals(tableSource)){
            tableName="ACCOUNTANT_GLVCHER_ABS";
        }
        map.put("tableName",tableName);
        if ("1".equals(tableSource)){
            map.put("processType",processType);
            if ("0".equals(processType)){
                map.put("processType",null);
            }
        }

        List<AccountantGlvcher> tAmsGlvchers =
                tAmsGlvcherSelfMapper.selectListByManagementIdAndDateAndFlowNo(map);
        List<TAmsGlvcherDTO> tAmsGlvcherDtos = BeanMapping.copyList(tAmsGlvchers, TAmsGlvcherDTO.class);
        if (tAmsGlvcherDtos != null) {
            tAmsGlvcherDtos.forEach(tAmsGlvcher -> {
                TPmsGlacgnDTO byGlAcctAndCurrCode =
                        glacgnService.findByGlAcctAndCurrCode(tAmsGlvcher.getOrganizationNumber(),
                                tAmsGlvcher.getBranchid(), tAmsGlvcher.getGlAcct(), tAmsGlvcher.getCurrCode());
                if (byGlAcctAndCurrCode == null) {
                    tAmsGlvcher.setGlAcctName(String.format("Subject does not exist in subject parameter table:[%s][%s]", tAmsGlvcher.getGlAcct(),
                            tAmsGlvcher.getCurrCode()));
                }else {
                    tAmsGlvcher.setGlAcctName(byGlAcctAndCurrCode.getGlAcctName());
                }
                TransactionCodeResDTO transactionCode=null;
                if (tAmsGlvcher.getVpTxnCode() != null) {
                    try {
                        transactionCode = transactionCodeService.findTransactionCode(tAmsGlvcher.getOrganizationNumber(),
                                tAmsGlvcher.getVpTxnCode());
                    }catch (Exception e){
                        logger.error("Current parameter is not defined: vpTxnCode={}", tAmsGlvcher.getVpTxnCode(), e);
                    }
                }
                if (transactionCode != null) {
                    tAmsGlvcher.setVpTxnCodeDesc(transactionCode.getDescription());
                }

            });
        }
        return new PageResultDTO(page, rows, page1.getTotal(),page1.getPages(),tAmsGlvcherDtos);
    }

}
