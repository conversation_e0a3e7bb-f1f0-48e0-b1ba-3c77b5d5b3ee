package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.AutoSignStatusEnum;
import com.anytech.anytxn.installment.base.enums.InstallEntryFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentDerateMethodEnum;
import com.anytech.anytxn.installment.base.enums.ProcessFlagEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallAutoRecordSelfMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallAutoSignSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallAutoRecord;
import com.anytech.anytxn.business.dao.installment.model.InstallAutoSign;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.installment.base.domain.bo.InstallAutoOrderBO;
import com.anytech.anytxn.installment.base.domain.bo.InstallAutoOrderRecordBO;
import com.anytech.anytxn.installment.base.domain.dto.InstallAutoRecordDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallAutoRecordService;
import com.anytech.anytxn.installment.base.service.IInstallEntryService;
import com.anytech.anytxn.installment.base.utils.ListUtils;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 自动分期下单记录
 *
 * <AUTHOR>
 * @date 2019-07-05
 **/
@Service
public class InstallAutoRecordServiceImpl implements IInstallAutoRecordService {
    private Logger logger = LoggerFactory.getLogger(InstallAutoRecordServiceImpl.class);

    @Autowired
    private InstallAutoRecordSelfMapper installAutoRecordSelfMapper;

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;

    @Autowired
    private InstallAutoSignSelfMapper installAutoSignSelfMapper;

    @Autowired
    private IInstallEntryService installEntryService;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Value("${anytxn.batch.stmt.max-insert:500}")
    private int maxInsert;
    @Value("${anytxn.batch.stmt.max-update:500}")
    private int maxUpdate;

    @Override
    public int getCount(String partitionKey) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installAutoRecordSelfMapper.getCount(partitionKey0, partitionKey1);
    }

    @Override
    public List<String> queryIds(String partitionKey, List<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installAutoRecordSelfMapper.queryIds(partitionKey0, partitionKey1, rowNumbers);
    }

    /**
     * 批量自动下单记录process
     *
     * @param installAutoSign
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public InstallAutoOrderRecordBO batchAutoRecordProcess(InstallAutoSign installAutoSign) {
        logger.info("Batch auto record process started: organizationNumber={}, cardNumber={}", installAutoSign.getOrganizationNumber(), installAutoSign.getCardNumber());
        InstallAutoOrderRecordBO installAutoOrderRecordBO = null;
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", installAutoSign.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installAutoSign.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", installAutoSign.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("Organization info not found: organizationNumber={}", installAutoSign.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT );

        }
        LocalDate today = organizationInfo.getToday();
        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
        if (Objects.equals(AutoSignStatusEnum.TAKE_EFFECT.getCode(), installAutoSign.getStatus())) {
            installAutoOrderRecordBO = new InstallAutoOrderRecordBO();
            if (installAutoSign.getFromDate().compareTo(today) <= 0 && installAutoSign.getEndDate().compareTo(today) >= 0) {
                installAutoOrderRecordBO.setInstallAutoSign(installAutoSign);
                installAutoOrderRecordBO.setToday(today);
            }
            if (installAutoSign.getEndDate().compareTo(today) >= 0 && installAutoSign.getEndDate().isBefore(nextProcessingDay)) {
                installAutoSign.setStatus(AutoSignStatusEnum.INVALID.getCode());
                installAutoOrderRecordBO.setUpdateFlag(true);
            }

        //原来的writer的逻辑迁移了过来
        InstallAutoSign signDTO = installAutoOrderRecordBO.getInstallAutoSign();
        Map<String, Object> manageStateMap = new HashMap<>(16);
        if(Objects.nonNull(signDTO)){
            AccountStatementInfo accountStatementInfo = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(signDTO.getAccountManagementId(), installAutoOrderRecordBO.getToday());
            AccountStatementInfoDTO accountStatementInfoDTO = BeanMapping.copy(accountStatementInfo, AccountStatementInfoDTO.class);
            manageStateMap.put(accountStatementInfoDTO.getAccountManagementId(), accountStatementInfoDTO.getStatementId());
            manageStateMap.put(accountStatementInfoDTO.getStatementId(), accountStatementInfoDTO.getCloseBalance().subtract(accountStatementInfoDTO.getTotalDueAmount()));
        }
        Map<String, BigDecimal> balanceMap = new HashMap<>(16);
        Map<String, BigDecimal> postAmoutMap = new HashMap<>(16);
        if (Objects.nonNull(signDTO) && Objects.nonNull(manageStateMap.get(signDTO.getAccountManagementId()))) {
            Map<String, String> postMap = new HashMap<>(16);
            String cardNumber = signDTO.getCardNumber();
            String accountManagementId = signDTO.getAccountManagementId();
            String accountStatementId = String.valueOf(manageStateMap.get(signDTO.getAccountManagementId()));
            postMap.put("cardNumber", cardNumber);
            postMap.put("accountManagementId", accountManagementId);
            postMap.put("accountStatementId", accountStatementId);
            balanceMap.put(cardNumber, new BigDecimal(String.valueOf(manageStateMap.get(postMap.get("accountStatementId")))));
            Map<String, BigDecimal> resultMap = postedTransactionSelfMapper.sumPostingAmout(cardNumber, accountManagementId, accountStatementId);
            postAmoutMap.put(cardNumber, new BigDecimal(String.valueOf(resultMap.get("postingAmount"))));
        }
            if (signDTO != null && postAmoutMap.get(signDTO.getCardNumber()) != null) {
                BigDecimal sumPostingAmount = postAmoutMap.get(signDTO.getCardNumber());
                BigDecimal stateAmount = balanceMap.get(signDTO.getCardNumber());
                BigDecimal postAmout = sumPostingAmount.compareTo(stateAmount) > 0 ? stateAmount : sumPostingAmount;
                InstallAutoRecord autoRecord = new InstallAutoRecord();
                autoRecord.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
                autoRecord.setOrganizationNumber(signDTO.getOrganizationNumber());
                autoRecord.setAccountManagementId(signDTO.getAccountManagementId());
                autoRecord.setCardNumber(signDTO.getCardNumber());
                autoRecord.setAutoSignType(signDTO.getAutoSignType());
                autoRecord.setProcessDate(installAutoOrderRecordBO.getToday());
                autoRecord.setInstallProductCode(signDTO.getInstallProductCode());
                autoRecord.setInstallAmount(postAmout);
                autoRecord.setProcessFlag(ProcessFlagEnum.NO_HANDLED.getCode());
                autoRecord.setCreateTime(LocalDateTime.now());
                autoRecord.setUpdateTime(LocalDateTime.now());
                autoRecord.setUpdateBy("admin");
                autoRecord.setVersionNumber(1L);
                installAutoOrderRecordBO.setInstallAutoRecord(autoRecord);
            }
            installAutoOrderRecordBO.setInstallAutoSign(null);
            if (installAutoOrderRecordBO.getUpdateFlag() != null && installAutoOrderRecordBO.getUpdateFlag()) {
                installAutoOrderRecordBO.setInstallAutoSign(installAutoSign);
            }
        }
        return installAutoOrderRecordBO;
    }

    /**
     * 批量自动下单记录writer
     *
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchAutoRecordWriter(List<InstallAutoOrderRecordBO> list) {
        List<InstallAutoRecord> autoRecordList = new ArrayList<>();
        List<InstallAutoSign> signDTOList = new ArrayList<>();
        list.stream().forEach(installAutoOrderRecordBO -> {
            if (installAutoOrderRecordBO != null) {
                if (!ObjectUtils.isEmpty(installAutoOrderRecordBO.getInstallAutoRecord())) {
                    autoRecordList.add(installAutoOrderRecordBO.getInstallAutoRecord());
                }
                if (!ObjectUtils.isEmpty(installAutoOrderRecordBO.getInstallAutoSign())) {
                    signDTOList.add(installAutoOrderRecordBO.getInstallAutoSign());
                }
            }
        });

        if (!autoRecordList.isEmpty() && autoRecordList.size() > maxInsert) {
            List<List<InstallAutoRecord>> insertLists = ListUtils.fixedGrouping(autoRecordList, maxInsert);
            for (List<InstallAutoRecord> installAutoRecordList : insertLists) {
                int i = installAutoRecordSelfMapper.insertAutoRecordBatch(installAutoRecordList);
                if (i != installAutoRecordList.size()) {
                    logger.error("Batch insert count mismatch for INSTALL_AUTH_RECORD table: expected={}, actual={}", installAutoRecordList.size(), i);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_RECORD_BATCH_INSERT_FAULT);
                }
                logger.info("Batch insert completed: count={}", i);
            }
        }else if(!autoRecordList.isEmpty() && autoRecordList.size() <= maxInsert){
            int i = installAutoRecordSelfMapper.insertAutoRecordBatch(autoRecordList);
            if (i != autoRecordList.size()) {
                logger.error("Batch insert count mismatch for INSTALL_AUTH_RECORD table: expected={}, actual={}", autoRecordList.size(), i);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_RECORD_BATCH_INSERT_FAULT);
            }
            logger.info("Batch insert completed: count={}", i);
        }
        //批量更新，防止单个sql过长，乐观锁控制更新准确性
        if (!signDTOList.isEmpty() && signDTOList.size() > maxUpdate) {
            List<List<InstallAutoSign>> acctUpdateList = ListUtils.fixedGrouping(signDTOList, maxUpdate);
            for (List<InstallAutoSign> installAutoSignList : acctUpdateList) {
                int updateacctCounts = installAutoSignSelfMapper.batchUpdateStatus(BeanMapping.copyList(installAutoSignList, InstallAutoSign.class));
                //乐观锁控制
                if (updateacctCounts != installAutoSignList.size()) {
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_OPT_LOCK_CONFLICT);
                }
            }
        } else if(!signDTOList.isEmpty() && signDTOList.size() <= maxUpdate){
            int updateacctCounts = installAutoSignSelfMapper.batchUpdateStatus(BeanMapping.copyList(signDTOList, InstallAutoSign.class));
            //乐观锁控制
            if (updateacctCounts != signDTOList.size()) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_OPT_LOCK_CONFLICT);
            }
        }
    }

    /**
     * 自动分期批量下单
     *
     * @param installAutoRecordDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public InstallAutoOrderBO batchAutoOrderProcess(InstallAutoRecord installAutoRecordDTO) {
        InstallAutoOrderBO installAutoOrderBO = new InstallAutoOrderBO();
        InstallEntryDTO installEntryDTO = new InstallEntryDTO();
        installEntryDTO.setFunctionCode(InstallEntryFunctionCodeEnum.AUTO_INSTALL_ENTRY.getCode());
        installEntryDTO.setOrganizationNumber(installAutoRecordDTO.getOrganizationNumber());
        installEntryDTO.setAccountManagementId(installAutoRecordDTO.getAccountManagementId());
        installEntryDTO.setCardNumber(installAutoRecordDTO.getCardNumber());
        installEntryDTO.setProductCode(installAutoRecordDTO.getInstallProductCode());
        installEntryDTO.setInstallType(installAutoRecordDTO.getAutoSignType());
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", installEntryDTO.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", installEntryDTO.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("Organization info not found: organizationNumber={}", installEntryDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);

        }
        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
        installEntryDTO.setTransactionDate(nextProcessingDay);
        installEntryDTO.setInstallCcy("156");
        installEntryDTO.setInstallAmount(installAutoRecordDTO.getInstallAmount());
        installEntryDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        installEntryDTO.setInstallTotalFee(BigDecimal.ZERO);
        installEntryDTO.setInstallFeeRate(BigDecimal.ZERO);
        installEntryDTO.setInstallDerateMethod(InstallmentDerateMethodEnum.REDUCTION_NO.getCode());
        installEntryDTO.setInstallDerateValue(BigDecimal.ZERO);
        installAutoOrderBO.setId(installAutoRecordDTO.getId());
        installAutoOrderBO.setInstallEntryDTO(installEntryDTO);
        installAutoOrderBO.setVersionNumber(installAutoRecordDTO.getVersionNumber());
        return installAutoOrderBO;
    }

    /**
     * 自动分期批量下单writer
     *
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchAutoOrderWriter(List<? extends InstallAutoOrderBO> list) {
        List<InstallAutoRecordDTO> recordList = new ArrayList<>();
        for (InstallAutoOrderBO installAutoOrderBO : list) {
            InstallAutoRecordDTO autoRecord = new InstallAutoRecordDTO();
            //处理标志，已处理
            autoRecord.setProcessFlag(ProcessFlagEnum.ALREADY_HANDLED.getCode());
            autoRecord.setVersionNumber(installAutoOrderBO.getVersionNumber());
            //分期录入
            try {
                logger.info("Calling installEntryService.entry: installRecordId={}", installAutoOrderBO.getInstallEntryDTO().getInstallRecordId());
                installEntryService.entry(installAutoOrderBO.getInstallEntryDTO());
                logger.info("installEntryService.entry completed: installRecordId={}", installAutoOrderBO.getInstallEntryDTO().getInstallRecordId());
                autoRecord.setId(installAutoOrderBO.getId());
                autoRecord.setInstallRecordId(installAutoOrderBO.getInstallEntryDTO().getInstallRecordId());
                autoRecord.setReturnCode("200");
                autoRecord.setReturnDesc("成功");
            } catch (AnyTxnInstallException e) {
                logger.error("Auto installment batch order failed: installment entry call failed");
                autoRecord.setId(installAutoOrderBO.getId());
                autoRecord.setInstallRecordId(installAutoOrderBO.getInstallEntryDTO().getInstallRecordId());
                autoRecord.setReturnCode(e.getErrCode());
                autoRecord.setReturnDesc(e.getErrMsg());
            }
            recordList.add(autoRecord);
        }
        //批量更新，防止单个sql过长，乐观锁控制更新准确性
        if (!recordList.isEmpty() && recordList.size() > maxUpdate) {
            List<List<InstallAutoRecordDTO>> acctUpdateList = ListUtils.fixedGrouping(recordList, maxUpdate);
            for (List<InstallAutoRecordDTO> installAutoRecordList : acctUpdateList) {
                int updateacctCounts = installAutoRecordSelfMapper.batchUpdateStatus(BeanMapping.copyList(installAutoRecordList, InstallAutoRecord.class));
                //乐观锁控制
                if (updateacctCounts != installAutoRecordList.size()) {
                    logger.error("Optimistic lock conflict detected while updating installment auto record status");
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_OPT_LOCK_CONFLICT);
                }
            }
        } else if(!recordList.isEmpty() && recordList.size() <= maxUpdate){
            int updateacctCounts = installAutoRecordSelfMapper.batchUpdateStatus(BeanMapping.copyList(recordList, InstallAutoRecord.class));
            //乐观锁控制
            if (updateacctCounts != recordList.size()) {
                logger.error("Optimistic lock conflict detected while updating installment auto record status");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_OPT_LOCK_CONFLICT);
            }
        }
    }
}
