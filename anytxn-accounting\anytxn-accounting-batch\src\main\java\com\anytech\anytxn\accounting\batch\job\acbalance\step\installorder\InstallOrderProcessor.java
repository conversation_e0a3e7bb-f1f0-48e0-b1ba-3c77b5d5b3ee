package com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder;

import com.anytech.anytxn.accounting.base.service.IBalService;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
public class InstallOrderProcessor implements ItemProcessor<InstallOrderDTO, AccountantGlinsbalDTO> {

    private static final Logger logger = LoggerFactory.getLogger(InstallOrderProcessor.class);

    @Autowired
    private IBalService balService;

    @Override
    public AccountantGlinsbalDTO process(InstallOrderDTO item) throws Exception {
        logger.info("Install order processing started: orderId={}", item.getOrderId());
        AccountantGlinsbalDTO result = balService.installOrderSum(item);
        logger.info("Install order processing completed: orderId={}", item.getOrderId());
        return result;
    }
}
