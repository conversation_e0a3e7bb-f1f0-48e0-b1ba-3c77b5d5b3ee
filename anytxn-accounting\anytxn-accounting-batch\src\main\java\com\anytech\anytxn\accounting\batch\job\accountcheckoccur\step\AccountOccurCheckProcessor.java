package com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.accounting.base.service.IAccountsOccurCheckService;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/26
 */
public class AccountOccurCheckProcessor implements ItemProcessor<TPmsGlacgnDTO, List<TAmsGlbalchkOccurDTO>> {

    private static final Logger logger = LoggerFactory.getLogger(AccountOccurCheckProcessor.class);

    @Autowired
    private IAccountsOccurCheckService accountsOccurCheckService;

    @Override
    public List<TAmsGlbalchkOccurDTO> process(TPmsGlacgnDTO item) throws Exception {
        logger.info("Account occur check processing started: glacgnId={}", item.getGlacgnId());
        List<TAmsGlbalchkOccurDTO> result = accountsOccurCheckService.caculateGlCheckOcur(item);
        logger.info("Account occur check processing completed: glacgnId={}", item.getGlacgnId());
        return result;
    }
}
