package com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step;

import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/13
 */
public class AccountOccurCheckReader extends JdbcPagingItemReader<TPmsGlacgnDTO> {

    private static final Logger logger = LoggerFactory.getLogger(AccountOccurCheckReader.class);

    public AccountOccurCheckReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(TPmsGlacgnDTO.class));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        providerFactoryBean.setSelectClause(" ID, ORGANIZATION_NUMBER, BRANCHID, GL_ACCT, CURR_CODE, GL_CLASS, GL_ACCT_NAME, GL_OF_CHECK_FLAG, GL_TOTAL_CHECK_FLAG, GL_ANNUAL_FLAG, GL_PL_NUM, GL_PL_NUM_NAME, GL_BANK_FLAG, GL_BAL_FLAG, CREATE_TIME, UPDATE_TIME, UPDATE_BY, VERSION_NUMBER,GL_ACCT_BALCHK_OPTION");
        providerFactoryBean.setFromClause("PARM_PMS_GLACGN");
        String orgConditionStr = " and ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        providerFactoryBean.setWhereClause("GL_TOTAL_CHECK_FLAG = 'Y' and GL_ACCT_BALCHK_OPTION is not null"+orgConditionStr);
        providerFactoryBean.setDataSource(dataSource);
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("Failed to create paging query provider", e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT, AccountRepDetailEnum.BATCH_PAGE_QUERY_ERROR);
        }
    }
}

