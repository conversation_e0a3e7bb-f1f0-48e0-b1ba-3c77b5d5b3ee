package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * 文件输出操作类
 *
 * <AUTHOR>
 * @date 2023-04-28
 */
@Service
public class FileWriteHandle {

    private static final Logger logger = LoggerFactory.getLogger(FileWriteHandle.class);

    private String systemName = System.getProperties().getProperty("os.name");

    public String getPathForSystem() {
        return systemName.contains("Windows") ? "\\" : "/";
    }

    public void inputHeader(File file,String header){
        try {
            FileWriter fileWriter = new FileWriter(file);
            fileWriter.write(header);
            fileWriter.flush();
            fileWriter.close();
        } catch (IOException e) {
            logger.error("Failed to write header to file: file={}", file.getPath(), e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_PARAM_EMPTY_FAIL);
        }
    }

    /**
     * 写出文件
     *
     * @param exportFilePath   输出文件的路径
     * @param stringList 写出文件的数据
     */
    public void outFile(String exportFilePath, List<String> stringList) {
        File file = new File(exportFilePath);
        try {
            if (file.exists()) {
                FileUtils.deleteQuietly(file);
            }
            // 创建父级目录
            if (file.isDirectory()) {
                boolean mkdirs = file.mkdirs();
                if (!mkdirs) {
                    logger.error("Failed to create directory: path={}", file.getPath());
                    throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_PARAM_EMPTY_FAIL);
                }
            }
            FileUtils.writeLines(file, "UTF-8", stringList, Boolean.TRUE);
        } catch (IOException e) {
            logger.error("Failed to write file: path={}", exportFilePath, e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_PARAM_EMPTY_FAIL);
        }
    }
    public void outFile(File file, List<String> stringList) {
        try {
            if (file.exists()) {
                FileUtils.deleteQuietly(file);
            }
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            FileUtils.writeLines(file, "UTF-8", stringList, Boolean.TRUE);
        } catch (IOException e) {
            logger.error("Failed to write file: path={}", file.getPath(), e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_PARAM_EMPTY_FAIL);
        }
    }
}
