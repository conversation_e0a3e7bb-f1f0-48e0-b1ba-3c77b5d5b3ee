package com.anytech.anytxn.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlOriRecordDetail;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlRecordSum;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlTxnSum;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherOriDetailInfo;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherSpecialInfo;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Service
public class GlvcherFileServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(GlvcherFileServiceImpl.class);
    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    private Map<String, ParmTransactionCode> transactionCodeMap = new HashMap<>(60);

    private Map<String, TPmsGlacgn> tpmsGlacgnSelfMap = new HashMap<>(60);

    private Map<String, ParmCardProductInfo> cardProductInfoMap = new HashMap<>(60);


    //科目汇总
    public AccountantGlRecordSum buildGlRecordSum(SubjectRecordSum subjectIdAndDrCr) {
        logger.info("Building GL record sum: subjectCodeId={}, org={}, data={}", 
                   subjectIdAndDrCr.getSubjectCodeId(), OrgNumberUtils.getOrg(), 
                   subjectIdAndDrCr.getSubjectCodeId() + "," + subjectIdAndDrCr.getTotalAmount() + "," + subjectIdAndDrCr.getCurrency());
        AccountantGlRecordSum accountantGlRecordSum = new AccountantGlRecordSum();
        accountantGlRecordSum.setSumDate(StringUtils.isBlank(subjectIdAndDrCr.getSumDate())?"":subjectIdAndDrCr.getSumDate());
        accountantGlRecordSum.setSubjectCodeId(StringUtils.isBlank(subjectIdAndDrCr.getSubjectCodeId())?"":subjectIdAndDrCr.getSubjectCodeId());
        accountantGlRecordSum.setSubjectDesc(StringUtils.isBlank(subjectIdAndDrCr.getSubjectDesc())?"":subjectIdAndDrCr.getSubjectDesc());
        accountantGlRecordSum.setTotalAmt(ObjectUtils.isEmpty(subjectIdAndDrCr.getTotalAmount())?BigDecimal.ZERO:subjectIdAndDrCr.getTotalAmount());
        accountantGlRecordSum.setCurrency(StringUtils.isBlank(subjectIdAndDrCr.getCurrency())?"":subjectIdAndDrCr.getCurrency());
        accountantGlRecordSum.setAccountProduct(StringUtils.isBlank(subjectIdAndDrCr.getAccountProduct())?"":subjectIdAndDrCr.getAccountProduct());
        accountantGlRecordSum.setDebitCreditSide(StringUtils.isBlank(subjectIdAndDrCr.getDebitCreditSide())?"":subjectIdAndDrCr.getDebitCreditSide());
        accountantGlRecordSum.setModuleFlag(StringUtils.isBlank(subjectIdAndDrCr.getModuleFlag())?"":subjectIdAndDrCr.getModuleFlag());
        accountantGlRecordSum.setCrdOrganization(StringUtils.isBlank(subjectIdAndDrCr.getCrdOrganization()) ? "": subjectIdAndDrCr.getCrdOrganization());

        logger.info("GL record sum completed: subjectCodeId={}, totalAmount={}, currency={}", 
                   accountantGlRecordSum.getSubjectCodeId(), accountantGlRecordSum.getTotalAmt(), accountantGlRecordSum.getCurrency());
        return accountantGlRecordSum;
    }

    //交易汇总
    public AccountantGlTxnSum buildGlTxnSum(AccountantGlvcherSpecialInfo specialInfoDTO, LocalDate today) {
        logger.info("Building GL transaction sum: glAcct={}, org={}, data={}", 
                   specialInfoDTO.getGlAcct(), OrgNumberUtils.getOrg(), 
                   specialInfoDTO.getGlAcct() + "," + specialInfoDTO.getSum() + "," + specialInfoDTO.getTxnCodeOrig());
        AccountantGlTxnSum accountantGlTxnSum = new AccountantGlTxnSum();
        SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH);
        AccountantGlvcherSelfMapper accountantGlvcherSelfMapper = session.getMapper(AccountantGlvcherSelfMapper.class);
        TPmsGlacgn tPmsGlacgn = getParmGlacgn(specialInfoDTO.getGlAcct(), session);
        if (ObjectUtils.isEmpty(tPmsGlacgn)) {
            logger.info("Subject code not found: glAcct={}", specialInfoDTO.getGlAcct());
        }
        String startDate = specialInfoDTO.getPostingDate().toString();
        String[] str = startDate.split("-");
        String startDateStr = str[0] + str[1] + str[2];
        if (specialInfoDTO.getPostingDate().getYear() != today.getYear()){
            logger.info("Skipping voucher not from current year");
            return null;
        }
        if (specialInfoDTO.getPostingDate().getMonthValue() != today.getMonthValue()) {
            logger.info("Skipping voucher not from current month");
            return null;
        }

        accountantGlTxnSum.setDateRange(startDateStr);
        accountantGlTxnSum.setTotalAmt(specialInfoDTO.getSum());
        accountantGlTxnSum.setDebitCreditSide(specialInfoDTO.getDrcr());
        accountantGlTxnSum.setSubjectCodeId(specialInfoDTO.getGlAcct());
        if (ObjectUtils.isNotEmpty(tPmsGlacgn)) {
            accountantGlTxnSum.setSubjectDesc(tPmsGlacgn.getGlAcctName());
        } else {
            accountantGlTxnSum.setSubjectDesc("can not find description");
        }
        accountantGlTxnSum.setTxnCode(specialInfoDTO.getTxnCodeOrig());
        ParmTransactionCode parmTransactionCode = getParmTransactionCode(specialInfoDTO.getTxnCodeOrig(), session);
        accountantGlTxnSum.setTxnDescription(ObjectUtils.isEmpty(parmTransactionCode) ? "no txn decs" : parmTransactionCode.getDescription());
        accountantGlTxnSum.setCurrency(specialInfoDTO.getCurrCode());
        accountantGlTxnSum.setSource(StringUtils.isBlank(specialInfoDTO.getSubchannelId()) ? "" : specialInfoDTO.getSubchannelId());

        logger.info("GL transaction sum completed: glAcct={}, totalAmount={}, txnCode={}", 
                   accountantGlTxnSum.getSubjectCodeId(), accountantGlTxnSum.getTotalAmt(), accountantGlTxnSum.getTxnCode());
        return accountantGlTxnSum;
    }

    //原始明细
    public AccountantGlOriRecordDetail buildOriRecordDetail(AccountantGlvcherOriDetailInfo detailInfo,
                                                            OrganizationInfoResDTO organizationInfo) {
        /*LocalDate today = organizationInfo.getToday();
        if (detailInfo.getPostingDate().compareTo(today) != 0){
            return null;
        }*/
        //log.info("科目号：{} 机构号：{},当前查到的明细：{}", detailInfo.getGlAcct(), OrgNumberUtils.getOrg(),JSON.toJSONString(detailInfo));
        AccountantGlOriRecordDetail accountantGlOriRecordDetail = new AccountantGlOriRecordDetail();
        SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH);

        TPmsGlacgn tPmsGlacgn = getParmGlacgn(detailInfo.getGlAcct(), session);

        CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper = session.getMapper(CardAuthorizationInfoSelfMapper.class);
        //AccountManagementInfoMapper accountManagementInfoMapper = session.getMapper(AccountManagementInfoMapper.class);
        //AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(detailInfo.getAccountManagementId());

        CardAuthorizationInfo cardAuthorizationInfo = null;
        String strDate = null;
        String date = detailInfo.getPostingDate().toString();
        String[] str = date.split("-");
        String strDate2 = str[0] + str[1] + str[2];
        accountantGlOriRecordDetail.setSumDate(strDate2);
        accountantGlOriRecordDetail.setSubjectCodeId(detailInfo.getGlAcct());
        accountantGlOriRecordDetail.setAccountManagementInfo(detailInfo.getAccountManagementId());
        if (ObjectUtils.isNotEmpty(tPmsGlacgn)) {
            accountantGlOriRecordDetail.setSubjectDesc(tPmsGlacgn.getGlAcctName());
        } else {
            accountantGlOriRecordDetail.setSubjectDesc("can not find description");
        }
        accountantGlOriRecordDetail.setDebitCreditSide(StringUtils.isBlank(detailInfo.getDrcr()) ? "" : detailInfo.getDrcr());
        accountantGlOriRecordDetail.setTotalAmt(detailInfo.getGlAmount());
        accountantGlOriRecordDetail.setSumCurrency(StringUtils.isBlank(detailInfo.getCurrCode()) ? "" : detailInfo.getCurrCode());
        accountantGlOriRecordDetail.setTxnCode(StringUtils.isBlank(detailInfo.getTxnCodeOrig()) ? "" : detailInfo.getTxnCodeOrig());
        accountantGlOriRecordDetail.setAccountProduct(StringUtils.isBlank(detailInfo.getAcctLogo()) ? "" : detailInfo.getAcctLogo());
        accountantGlOriRecordDetail.setVaNum(ObjectUtils.isEmpty(detailInfo.getVaNumber()) ? "" : detailInfo.getVaNumber());

        if (ObjectUtils.isNotEmpty(detailInfo.getTxnDate())) {
            String date2 = detailInfo.getTxnDate().toString();
            String[] str2 = date2.split("-");
            if (str2[2].length() == 8) {
                str2[2] = str2[2] + ":00";
            } else if (str2[2].length() == 5) {
                str2[2] = str2[2] + ":00:00";
            }
            strDate = str2[0] + str2[1] + str2[2];
        }
        cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumberInStatementFile(detailInfo.getCrdNumber());
        if (ObjectUtils.isNotEmpty(cardAuthorizationInfo)) {
            String crdPro = cardAuthorizationInfo.getProductNumber();
            ParmCardProductInfo parmCardProductInfo = getCardProductInfo(crdPro.trim(), session);
            String crdOrg = parmCardProductInfo.getScheme();
            accountantGlOriRecordDetail.setCardProduct(crdPro);
            accountantGlOriRecordDetail.setCardOrganization(crdOrg);
            //log.info("该卡{}卡产品：{}，卡组织：{}", detailInfo.getCrdNumber(), crdPro, crdOrg);
        } else {
            if (!"M".equals(detailInfo.getModuleFlag().trim())) {
                ParmCardProductInfo parmCardProductInfo = null;
                if (StringUtils.isNotBlank(detailInfo.getAcctLogo())) {
                    //acctLogo might empty
                    parmCardProductInfo = getCardProductInfo(detailInfo.getAcctLogo(), session);
                }
                if (!ObjectUtils.isEmpty(parmCardProductInfo)) {
                    String crdOrg = parmCardProductInfo.getScheme();
                    String crdPdt = parmCardProductInfo.getProductNumber();
                    accountantGlOriRecordDetail.setCardProduct(crdPdt);
                    accountantGlOriRecordDetail.setCardOrganization(crdOrg);
                } else {
                    accountantGlOriRecordDetail.setCardProduct(StringUtils.isBlank(detailInfo.getCrdProductNumber()) ? "" : detailInfo.getCrdProductNumber());
                    accountantGlOriRecordDetail.setCardOrganization("");
                }
                //log.info("非收单授权来的卡，但查不到这张卡{}的卡授权，只能直接拿流水里的卡产品：{}", detailInfo.getCrdNumber(), detailInfo.getCrdProductNumber());
            } else {
                accountantGlOriRecordDetail.setCardProduct(StringUtils.isBlank(detailInfo.getCrdProductNumber()) ? "" : detailInfo.getCrdProductNumber());
                accountantGlOriRecordDetail.setCardOrganization(StringUtils.isBlank(detailInfo.getPlatformId()) ? "" : detailInfo.getPlatformId());
                //log.info("从收单授权来的卡，查不到这张卡{}的卡授权,卡产品直接给空，卡组织:{}", detailInfo.getCrdNumber(),detailInfo.getPlatformId());
            }
        }
        accountantGlOriRecordDetail.setTxnDate(ObjectUtils.isEmpty(detailInfo.getTxnDate()) ? "" : strDate);
        accountantGlOriRecordDetail.setTxnAmt(ObjectUtils.isEmpty(detailInfo.getTxnAmt()) ? BigDecimal.ZERO : detailInfo.getTxnAmt());
        if ("M".equals(detailInfo.getModuleFlag())) {
            accountantGlOriRecordDetail.setTxnDesc(StringUtils.isBlank(detailInfo.getTxnDescription()) ? "no ACQ txn decs" : detailInfo.getTxnDescription());
        } else {
            ParmTransactionCode parmTransactionCode = getParmTransactionCode(detailInfo.getTxnCodeOrig(), session);
            accountantGlOriRecordDetail.setTxnDesc(ObjectUtils.isEmpty(parmTransactionCode) ? "no txn decs" : parmTransactionCode.getDescription());
        }
        accountantGlOriRecordDetail.setMerchantId(StringUtils.isBlank(detailInfo.getMerchantNumber()) ? "" : detailInfo.getMerchantNumber());
        accountantGlOriRecordDetail.setRrn(StringUtils.isBlank(detailInfo.getRrn()) ? "" : detailInfo.getRrn());
        accountantGlOriRecordDetail.setSource(StringUtils.isBlank(detailInfo.getTxnSource()) ? "" : detailInfo.getTxnSource());
        accountantGlOriRecordDetail.setCurrencyRate(StringUtils.isBlank(detailInfo.getCurrencyRate()) ? "" : detailInfo.getCurrencyRate());
        accountantGlOriRecordDetail.setTxnCurrency(StringUtils.isBlank(detailInfo.getTxnCurrency()) ? "" : detailInfo.getTxnCurrency());
        accountantGlOriRecordDetail.setCrdNumber(StringUtils.isBlank(detailInfo.getCrdNumber()) ? "" : detailInfo.getCrdNumber());
        accountantGlOriRecordDetail.setProductType(StringUtils.isBlank(detailInfo.getProductType()) ? "" : detailInfo.getProductType());
        if ("M".equals(detailInfo.getModuleFlag())) {
            accountantGlOriRecordDetail.setModuleFlag("acquiring");
        } else if ("0".equals(detailInfo.getModuleFlag())) {
            accountantGlOriRecordDetail.setModuleFlag("issuing");
        }
        accountantGlOriRecordDetail.setAcqId(StringUtils.isBlank(detailInfo.getAcqId()) ? "" : detailInfo.getAcqId());
        //V2,V3标志
        accountantGlOriRecordDetail.setVataCoupleIndicator(StringUtils.isBlank(detailInfo.getVataCoupleIndicator()) ? "" : detailInfo.getVataCoupleIndicator());

        return accountantGlOriRecordDetail;
    }

    private ParmTransactionCode getParmTransactionCode(String transactionCode, SqlSession session){
        ParmTransactionCode parmTransactionCode = this.transactionCodeMap.get(transactionCode);

        if (parmTransactionCode == null){
            ParmTransactionCodeSelfMapper parmTransactionCodeSelfMapper = session.getMapper(ParmTransactionCodeSelfMapper.class);
            parmTransactionCode = parmTransactionCodeSelfMapper.selectByOrgNumberAndCode(OrgNumberUtils.getOrg(), transactionCode);
            this.transactionCodeMap.put(transactionCode, parmTransactionCode);
        }
        return parmTransactionCode;
    }

    private TPmsGlacgn getParmGlacgn(String glCode, SqlSession session) {
        TPmsGlacgn tpmsGlacgn = this.tpmsGlacgnSelfMap.get(glCode);
        if (tpmsGlacgn == null) {
            TPmsGlacgnSelfMapper tpmsGlacgnSelfMapper = session.getMapper(TPmsGlacgnSelfMapper.class);
            // todo 添加了一个null报错临时调整
            tpmsGlacgn = tpmsGlacgnSelfMapper.selectByOrgAndGlAcct(OrgNumberUtils.getOrg(), glCode,null);
            this.tpmsGlacgnSelfMap.put(glCode, tpmsGlacgn);
        }
        return tpmsGlacgn;

    }


    private ParmCardProductInfo getCardProductInfo(String crdPro, SqlSession session) {
        ParmCardProductInfo parmCardProductInfo = this.cardProductInfoMap.get(crdPro);
        if (parmCardProductInfo == null){
            ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper = session.getMapper(ParmCardProductInfoSelfMapper.class);
            parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(OrgNumberUtils.getOrg(),crdPro);

            this.cardProductInfoMap.put(crdPro, parmCardProductInfo);
        }
        return parmCardProductInfo;
    }


}
