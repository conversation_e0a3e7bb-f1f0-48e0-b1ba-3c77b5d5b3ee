package com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder;

import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/3
 *
 * 分期订单读取
 */
public class InstallOrderReader extends JdbcPagingItemReader<InstallOrderDTO> {

    private static final Logger logger = LoggerFactory.getLogger(InstallOrderReader.class);

    public InstallOrderReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(InstallOrderDTO.class));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();

        providerFactoryBean.setSelectClause("ORDER_ID, ORGANIZATION_NUMBER, ACCOUNT_MANAGEMENT_ID, CARD_NUMBER, PRODUCT_CODE, TRANSACTION_DATE, ACQUIRE_REFERENCE_NO, AUTHORIZATION_CODE, INSTALLMENT_CCY, TYPE, STATUS, FIRST_POST_DATE, INSTALLMENT_AMOUNT, UNPOSTED_AMOUNT, FIRST_TERM_AMOUNT, TERM_AMOUNT, TERM, POSTED_TERM, TOTAL_FEE_AMOUNT, UNPOSTED_FEE_AMOUNT, FIRST_TERM_FEE, TERM_FEE, FEE_TERM, POSTED_FEE_TERM, PAYMENT_WAY, FEE_FLAG, FEE_RATE, FEE_DERATE_FLAG, DERATE_TERM, DERATE_FEE_AMOUNT, DISCOUNT_RATE, TOTAL_RECEIVED_FEE, TOTAL_DERATE_FEE, RECEIVED_PENATLY_AMOUNT, TOTAL_RETURNED_AMOUNT, LIMIT_CODE, MERCHANT_ID, MCC, UPDATE_BY, STATUS_UPDATE_DATE, TRANSACTION_DESC, CREATE_TIME, UPDATE_TIME, VERSION_NUMBER, ORIGIN_TRANSACTION_ID, LAST_MAINTAIN_DATE, TRANSACTION_CHANNEL, CUSTOMER_ID, GLOBAL_FLOW_NUMBER,ABS_STATUS,ABS_PRODUCT_CODE");
        providerFactoryBean.setFromClause("from INSTALL_ORDER");
        String orgConditionStr = " ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        providerFactoryBean.setWhereClause(orgConditionStr);
        Map<String, Order> keys = Maps.newHashMap();
        keys.put("ORDER_ID", Order.DESCENDING);
        providerFactoryBean.setSortKeys(keys);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("Failed to create paging query provider", e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT, AccountRepDetailEnum.BATCH_PAGE_QUERY_ERROR);
        }
    }
}
