package com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep;

import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.base.service.IGlVoucherService;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 根据交易会计流水生成传票
 * <AUTHOR>
public class AmsToVoucherProcessor implements ItemProcessor<AccountantGlams, GenerateVoucherBO> {

    private static final Logger logger = LoggerFactory.getLogger(AmsToVoucherProcessor.class);

    private OrganizationInfoResDTO organizationInfo;
    public AmsToVoucherProcessor(OrganizationInfoResDTO organizationInfo) {
        this.organizationInfo = organizationInfo;
    }

    @Autowired
    private IGlVoucherService glVoucherService;

    @Override
    public GenerateVoucherBO process(AccountantGlams tAmsGlams) {
        logger.info("GL voucher service call started: accountManagementId={}", tAmsGlams.getAccountManagementId());
        // 生成会计传票
        GenerateVoucherBO result = glVoucherService.amsToVoucher(tAmsGlams.getAccountManagementId(),this.organizationInfo);
        logger.info("GL voucher service call completed: accountManagementId={}", tAmsGlams.getAccountManagementId());
        return result;
    }
}
