package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.service.IGlAmsAcgmService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgmDTO;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgdSelfMapper;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgmSelfMapper;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgpSelfMapper;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgd;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgm;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgp;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@Service
public class GlAmsAcgmServiceImpl implements IGlAmsAcgmService {

    private static final Logger logger = LoggerFactory.getLogger(GlAmsAcgmServiceImpl.class);

    @Autowired
    private AccountantGlacgmSelfMapper amsGlacgmSelfMapper;
    @Autowired
    private AccountantGlacgdSelfMapper amsGlacgdSelfMapper;
    @Autowired
    private AccountantGlacgpSelfMapper accountantGlacgpSelfMapper;
    @Autowired
    private ITPmsGlacgnService tPmsGlacgnService;

    @Override
    public PageResultDTO<TAmsGlacgmDTO> getGlAmsAcgmByPage(Integer page, Integer rows, String postingDate) {
        String datem = null;
        if (postingDate != null) {
            String[] split = postingDate.split("-");
            datem = split[0] + split[1];
        }
        List<TAmsGlacgm> tAmsGlacgms;
        //返回对象 按科目汇总
        logger.info("Calling tPmsGlacgnService.findAll: org={}", OrgNumberUtils.getOrg());
        List<TPmsGlacgnDTO> tPmsGlacgns = tPmsGlacgnService.findAll(OrgNumberUtils.getOrg());
        logger.info("tPmsGlacgnService.findAll completed: count={}", tPmsGlacgns != null ? tPmsGlacgns.size() : 0);
        String finalDatem = datem;
        Map<String, TAmsGlacgm> resultMap = tPmsGlacgns.stream().map(tPmsGlacgn -> {
            TAmsGlacgm tAmsGlacgm = new TAmsGlacgm();
            tAmsGlacgm.setOrganizationNumber(tPmsGlacgn.getOrganizationNumber());
            tAmsGlacgm.setBranchid(tPmsGlacgn.getBranchid());
            tAmsGlacgm.setGlAcct(tPmsGlacgn.getGlAcct());
            tAmsGlacgm.setDateM(finalDatem);
            tAmsGlacgm.setCurr(tPmsGlacgn.getCurrCode());
            tAmsGlacgm.setGlClass(tPmsGlacgn.getGlClass());
            tAmsGlacgm.setGlName(tPmsGlacgn.getGlAcctName());
            tAmsGlacgm.setPrevBalDr(BigDecimal.ZERO);
            tAmsGlacgm.setPrevBalCr(BigDecimal.ZERO);
            tAmsGlacgm.setOccurDr(BigDecimal.ZERO);
            tAmsGlacgm.setOccurCr(BigDecimal.ZERO);
            tAmsGlacgm.setCurrBalDr(BigDecimal.ZERO);
            tAmsGlacgm.setCurrBalCr(BigDecimal.ZERO);
            return tAmsGlacgm;
        }).collect(Collectors.toMap(tAmsGlacgm -> tAmsGlacgm.getOrganizationNumber() + tAmsGlacgm.getBranchid()
                + tAmsGlacgm.getGlAcct() + tAmsGlacgm.getCurr(), tAmsGlacgm -> tAmsGlacgm));

        // 数据总数
        long total = 0L;
        // 数据总页数
        int pages = 0;
        // 是否需要手动分页
        boolean pageSelf = false;
        if (postingDate != null) {
            Page pageInfo = PageHelper.startPage(page, rows);
            //当月月余额表
            tAmsGlacgms = amsGlacgmSelfMapper.selectByMonth(datem, OrgNumberUtils.getOrg());
            if (CollectionUtils.isEmpty(tAmsGlacgms)) {
                //计算最近的金额
                tAmsGlacgms = caculate(resultMap, postingDate);
            } else {
                // 使用mybatis插件分页
                total = pageInfo.getTotal();
                pages = pageInfo.getPages();
            }
        } else {
            tAmsGlacgms = caculate(resultMap, postingDate);
            pageSelf = true;
        }
        if (CollectionUtils.isNotEmpty(tAmsGlacgms)) {
            // 手动分页
            if (pageSelf) {
                List<List<TAmsGlacgm>> partition = Lists.partition(tAmsGlacgms, rows);
                total = tAmsGlacgms.size();
                pages = partition.size();
                tAmsGlacgms = partition.get(page - 1);
            }
            tAmsGlacgms = tAmsGlacgms.stream().sorted(Comparator.comparing(TAmsGlacgm::getOrganizationNumber).
                            thenComparing(TAmsGlacgm::getGlAcct).thenComparing(TAmsGlacgm::getCurr)).collect(Collectors.toList());
        }
        return new PageResultDTO(page, rows, total, pages, tAmsGlacgms);
    }

    private List<TAmsGlacgm> caculate(Map<String, TAmsGlacgm> resultMap, String postingDate) {

        logger.info("Calling tPmsGlacgnService.selectCount");
        long l = tPmsGlacgnService.selectCount() + 1;
        logger.info("tPmsGlacgnService.selectCount completed: count={}", l - 1);
        //最近月月终余额
        List<TAmsGlacgm> lastMonthGl = amsGlacgmSelfMapper.selectLastMonth(l,OrgNumberUtils.getOrg());
        Map<String, TAmsGlacgm> acgmMap = acgmToMap(lastMonthGl);
        if (acgmMap != null) {
            //最近借方,贷方余额
            resultMap.forEach((s, tAmsGlacgm) -> {
                if (acgmMap.get(s) != null) {
                    tAmsGlacgm.setPrevBalDr(acgmMap.get(s).getCurrBalDr());
                    tAmsGlacgm.setPrevBalDr(acgmMap.get(s).getCurrBalDr());
                }
            });
        }
        //当日借方余额
        logger.info("Calling tPmsGlacgnService.selectCount for total calculation");
        long total = tPmsGlacgnService.selectCount()+1;
        logger.info("tPmsGlacgnService.selectCount for total completed: total={}", total - 1);
        List<TAmsGlacgd> amsGlacgdList = amsGlacgdSelfMapper.selectLastDay(total);
        if (CollectionUtils.isNotEmpty(amsGlacgdList)) {
            LocalDate dated = amsGlacgdList.get(0).getDateD();
            Map<String, TAmsGlacgd> acgdMap =
                    amsGlacgdList.stream().filter(tAmsGlacgd -> dated.compareTo(tAmsGlacgd.getDateD()) == 0).
                            collect(Collectors.toMap(tAmsGlacgd -> tAmsGlacgd.getOrganizationNumber() + tAmsGlacgd.getBranchid()
                                            + tAmsGlacgd.getGlAcct() + tAmsGlacgd.getCurr(),
                                    tAmsGlacgd -> tAmsGlacgd));
            resultMap.forEach((s, tAmsGlacgm) -> {
                String datem = dated.format(DateTimeFormatter.ISO_LOCAL_DATE
                ).substring(0,7);
                tAmsGlacgm.setDateM(datem);
                if (acgdMap.get(s) != null) {
                    tAmsGlacgm.setCurrBalDr(acgdMap.get(s).getCurrBalDr());
                    tAmsGlacgm.setCurrBalCr(acgdMap.get(s).getCurrBalCr());
                }
            });
        }
        List<TAmsGlacgp> tAmsGlacgps = null;
        if (postingDate != null) {
            String[] split = postingDate.split("-");
            tAmsGlacgps = accountantGlacgpSelfMapper.selectByTime(split[0], split[1],OrgNumberUtils.getOrg());
        }

        if (CollectionUtils.isNotEmpty(tAmsGlacgps)) {
            //当月发生额
            List<TAmsGlacgd> tAmsGlacgds =
                    amsGlacgdSelfMapper.selectAllThisMonth(tAmsGlacgps.get(0).getGlPeriodStart(),
                            tAmsGlacgps.get(0).getGlPeriodEnd());
            if (CollectionUtils.isNotEmpty(tAmsGlacgds)) {
                Map<String, TAmsGlacgd> acgdMap =
                        tAmsGlacgds.stream().collect(Collectors.toMap(tAmsGlacgd -> tAmsGlacgd.getOrganizationNumber() + tAmsGlacgd.getBranchid()
                                        + tAmsGlacgd.getGlAcct() + tAmsGlacgd.getCurr(),
                                tAmsGlacgd -> tAmsGlacgd));
                resultMap.forEach((s, tAmsGlacgm) -> {
                    if (acgdMap.get(s) != null) {
                        tAmsGlacgm.setOccurCr(acgdMap.get(s).getCurrBalCr());
                        tAmsGlacgm.setOccurDr(acgdMap.get(s).getCurrBalDr());
                    }
                });
            }
        }
        return new ArrayList<>(resultMap.values());
    }

    private Map<String, TAmsGlacgm> acgmToMap(List<TAmsGlacgm> tAmsGlacgms) {
        if (CollectionUtils.isNotEmpty(tAmsGlacgms)) {
            String datem = tAmsGlacgms.get(0).getDateM();
            List<TAmsGlacgm> list =
                    tAmsGlacgms.stream().filter(tAmsGlacgm -> Objects.equals(datem, tAmsGlacgm.getDateM())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().collect(Collectors.toMap(tAmsGlacgm -> tAmsGlacgm.getOrganizationNumber() + tAmsGlacgm.getBranchid()
                        + tAmsGlacgm.getGlAcct() + tAmsGlacgm.getCurr(), tAmsGlacgm -> tAmsGlacgm));
            }
            return null;
        }
        return null;
    }
}
