package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.service.IGlAmsAcgyService;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgdSelfMapper;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgmSelfMapper;
import com.anytech.anytxn.accounting.mapper.AccountantGlacgySelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgyDTO;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgd;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgm;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgy;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@Service
public class GlAmsAcgyServiceImpl implements IGlAmsAcgyService {

    private static final Logger logger = LoggerFactory.getLogger(GlAmsAcgyServiceImpl.class);
    @Autowired
    private AccountantGlacgySelfMapper amsGlacgySelfMapper;
    @Autowired
    private AccountantGlacgmSelfMapper amsGlacgmSelfMapper;
    @Autowired
    private AccountantGlacgdSelfMapper amsGlacgdSelfMapper;

    @Autowired
    private ITPmsGlacgnService tPmsGlacgnService;

    @Override
    public PageResultDTO<TAmsGlacgyDTO> getGlAmsAcgyPage(Integer page, Integer rows, String postingDate) {
        List<TAmsGlacgy> tAmsGlacgys;
        //返回对象 按科目汇总
        logger.info("Calling tPmsGlacgnService.findAll: org={}", OrgNumberUtils.getOrg());
        List<TPmsGlacgnDTO> tPmsGlacgns = tPmsGlacgnService.findAll(OrgNumberUtils.getOrg());
        logger.info("tPmsGlacgnService.findAll completed: count={}", tPmsGlacgns != null ? tPmsGlacgns.size() : 0);
        Map<String, TAmsGlacgy> resultMap = tPmsGlacgns.stream().map(tPmsGlacgn -> {
            TAmsGlacgy tAmsGlacgy = new TAmsGlacgy();
            tAmsGlacgy.setOrganizationNumber(tPmsGlacgn.getOrganizationNumber());
            tAmsGlacgy.setBranchid(tPmsGlacgn.getBranchid());
            tAmsGlacgy.setGlAcct(tPmsGlacgn.getGlAcct());
            tAmsGlacgy.setCurr(tPmsGlacgn.getCurrCode());
            tAmsGlacgy.setGlClass(tPmsGlacgn.getGlClass());
            tAmsGlacgy.setDateY(postingDate);
            tAmsGlacgy.setGlName(tPmsGlacgn.getGlAcctName());
            tAmsGlacgy.setPrevBalDr(BigDecimal.ZERO);
            tAmsGlacgy.setPrevBalCr(BigDecimal.ZERO);
            tAmsGlacgy.setOccurDr(BigDecimal.ZERO);
            tAmsGlacgy.setOccurCr(BigDecimal.ZERO);
            tAmsGlacgy.setCurrBalDr(BigDecimal.ZERO);
            tAmsGlacgy.setCurrBalCr(BigDecimal.ZERO);
            return tAmsGlacgy;
        }).collect(Collectors.toMap(tAmsGlacgy -> tAmsGlacgy.getOrganizationNumber() + tAmsGlacgy.getBranchid()
                + tAmsGlacgy.getGlAcct() + tAmsGlacgy.getCurr(), tAmsGlacgy -> tAmsGlacgy));

        // 数据总数
        long total = 0L;
        // 数据总页数
        int pages = 0;
        // 是否需要手动分页
        boolean pageSelf = false;
        if (postingDate != null) {
            Page pageInfo = PageHelper.startPage(page, rows);
            //当年余额表
            tAmsGlacgys = amsGlacgySelfMapper.selectByYear(postingDate,OrgNumberUtils.getOrg());
            if (CollectionUtils.isEmpty(tAmsGlacgys)) {
                //计算最近的金额
                tAmsGlacgys = caculate(resultMap, postingDate);
                pageSelf = true;
            } else {
                // 使用mybatis插件分页
                total = pageInfo.getTotal();
                pages = pageInfo.getPages();
            }
        } else {
            tAmsGlacgys = caculate(resultMap, null);
            pageSelf = true;
        }
        if (CollectionUtils.isNotEmpty(tAmsGlacgys)) {
            // 手动分页
            if (pageSelf) {
                List<List<TAmsGlacgy>> partition = Lists.partition(tAmsGlacgys, rows);
                total = tAmsGlacgys.size();
                pages = partition.size();
                tAmsGlacgys = partition.get(page - 1);
            }

            tAmsGlacgys = tAmsGlacgys.stream().sorted(Comparator.comparing(TAmsGlacgy::getOrganizationNumber).
                            thenComparing(TAmsGlacgy::getGlAcct).thenComparing(TAmsGlacgy::getCurr)).collect(Collectors.toList());
        }
        return new PageResultDTO(page, rows, total, pages, tAmsGlacgys);
    }

    private List<TAmsGlacgy> caculate(Map<String, TAmsGlacgy> resultMap, String datey) {
        //最近年年终余额
        List<TAmsGlacgy> lastYearGl = amsGlacgySelfMapper.selectAll(OrgNumberUtils.getOrg());
        Map<String, TAmsGlacgy> acgyMap = acgyToMap(lastYearGl);
        if (acgyMap != null) {
            //最近借方,贷方余额
            resultMap.forEach((s, tAmsGlacgy) -> {
                if (acgyMap.get(s) != null) {
                    tAmsGlacgy.setPrevBalDr(acgyMap.get(s).getCurrBalDr());
                    tAmsGlacgy.setPrevBalDr(acgyMap.get(s).getCurrBalDr());
                }
            });
        }
        logger.info("Calling tPmsGlacgnService.selectCount");
        long l = tPmsGlacgnService.selectCount() + 1;
        logger.info("tPmsGlacgnService.selectCount completed: count={}", l - 1);
        //当日借方余额
        List<TAmsGlacgm> amsGlacgmList = amsGlacgmSelfMapper.selectLastMonth(l,OrgNumberUtils.getOrg());
        if (CollectionUtils.isNotEmpty(amsGlacgmList)) {
            String datem = amsGlacgmList.get(0).getDateM();
            Map<String, TAmsGlacgm> acgdMap =
                    amsGlacgmList.stream().filter(tAmsGlacgm -> Objects.equals(datem, tAmsGlacgm.getDateM())).
                            collect(Collectors.toMap(tAmsGlacgd -> tAmsGlacgd.getOrganizationNumber() + tAmsGlacgd.getBranchid()
                                            + tAmsGlacgd.getGlAcct() + tAmsGlacgd.getCurr(),
                                    tAmsGlacgm -> tAmsGlacgm));
            resultMap.forEach((s, tAmsGlacgy) -> {
                String resultDatey = datem.substring(0, 4);
                tAmsGlacgy.setDateY(resultDatey);
                if (acgdMap.get(s) != null) {
                    tAmsGlacgy.setCurrBalDr(acgdMap.get(s).getCurrBalDr());
                    tAmsGlacgy.setCurrBalCr(acgdMap.get(s).getCurrBalDr());
                }
            });
        } else {

            List<TAmsGlacgd> amsGlacgdList = amsGlacgdSelfMapper.selectLastDay(l);
            if (CollectionUtils.isNotEmpty(amsGlacgdList)) {
                String resultDatey = amsGlacgdList.get(0).getDateD().format(DateTimeFormatter.ISO_LOCAL_DATE
                ).substring(0, 4);
                resultMap.forEach((s, tAmsGlacgy) -> {
                    tAmsGlacgy.setDateY(resultDatey);
                });
            }
        }


        //当年发生额
        List<TAmsGlacgm> tAmsGlacgms = null;
        if (datey != null) {
            tAmsGlacgms = amsGlacgmSelfMapper.selectAllThisYear(datey, OrgNumberUtils.getOrg());
        }
        if (CollectionUtils.isNotEmpty(tAmsGlacgms)) {
            Map<String, TAmsGlacgm> acgdMap =
                    tAmsGlacgms.stream().collect(Collectors.toMap(tAmsGlacgm -> tAmsGlacgm.getOrganizationNumber() + tAmsGlacgm.getBranchid()
                                    + tAmsGlacgm.getGlAcct() + tAmsGlacgm.getCurr(),
                            tAmsGlacgm -> tAmsGlacgm));
            resultMap.forEach((s, tAmsGlacgy) -> {
                if (acgdMap.get(s) != null) {
                    tAmsGlacgy.setOccurCr(acgdMap.get(s).getCurrBalDr());
                    tAmsGlacgy.setOccurCr(acgdMap.get(s).getCurrBalDr());
                }
            });
        }
        return new ArrayList<>(resultMap.values());
    }

    private Map<String, TAmsGlacgy> acgyToMap(List<TAmsGlacgy> tAmsGlacgys) {
        if (CollectionUtils.isNotEmpty(tAmsGlacgys)) {
            String datey = tAmsGlacgys.get(0).getDateY();
            List<TAmsGlacgy> list =
                    tAmsGlacgys.stream().filter(tAmsGlacgy -> Objects.equals(datey, tAmsGlacgy.getDateY())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().collect(Collectors.toMap(tAmsGlacgy -> tAmsGlacgy.getOrganizationNumber() + tAmsGlacgy.getBranchid()
                        + tAmsGlacgy.getGlAcct() + tAmsGlacgy.getCurr(), tAmsGlacgy -> tAmsGlacgy));
            }
            return null;
        }
        return null;
    }
}
