package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.base.service.IBalService;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @description:
 * @author: ZXL
 * @create: 2020-03-01 18:32
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class BalServiceImpl implements IBalService {

    private static final Logger logger = LoggerFactory.getLogger(BalServiceImpl.class);

    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     * 基于分期订单生成分期余额
     * @param installOrder
     * @return
     */
    @Override
    public AccountantGlinsbalDTO installOrderSum(InstallOrderDTO installOrder) {
        logger.info("Install order sum started: orderId={}, organizationNumber={}", 
                   installOrder.getOrderId(), installOrder.getOrganizationNumber());
        
        // 机构参数
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrder.getOrganizationNumber());
        logger.info("Organization info retrieved: organizationNumber={}, today={}", 
                   installOrder.getOrganizationNumber(), organizationInfo.getToday());
        // 分期余额表
        AccountantGlinsbalDTO tAmsGlinsbal = new AccountantGlinsbalDTO();
        tAmsGlinsbal.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        tAmsGlinsbal.setOrganizationNumber(installOrder.getOrganizationNumber());
        tAmsGlinsbal.setBranchid("DINERS");
        tAmsGlinsbal.setProcDate(organizationInfo.getToday());
        tAmsGlinsbal.setTermPostFeeAmt(BigDecimal.ZERO);
        if (installOrder.getOrderId() != null) {
            tAmsGlinsbal.setOrderId(String.valueOf(installOrder.getOrderId()));
            List<InstallPlan> installPlans =
                    installPlanSelfMapper.selectPlansByOrderId(installOrder.getOrderId());

            if (CollectionUtils.isNotEmpty(installPlans)) {
                List<InstallPlanDTO> installPlanDTOS = BeanMapping.copyList(installPlans, InstallPlanDTO.class);
                if (CustAccountBO.isBatch()) {
                    installPlanDTOS = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(installPlanDTOS, installOrder.getOrderId());
                }

                BigDecimal bigDecimal =
                        installPlanDTOS.stream().filter(installPlan -> "N".equals(installPlan.getTermStutus())).map(InstallPlanDTO::getAmortizeFee)
                                .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                tAmsGlinsbal.setTermPostFeeAmt(bigDecimal);
            }
        }
        tAmsGlinsbal.setAccountManagementId(installOrder.getAccountManagementId());
        tAmsGlinsbal.setCurrCode(installOrder.getInstallmentCcy());
        tAmsGlinsbal.setProdCode(installOrder.getProductCode());
        tAmsGlinsbal.setUnpostedAmt(installOrder.getUnpostedAmount());
        tAmsGlinsbal.setAbsStatus(installOrder.getAbsStatus());
        tAmsGlinsbal.setAssetNo(installOrder.getAbsProductCode());
        tAmsGlinsbal.setUpdateTime(LocalDateTime.now());
        tAmsGlinsbal.setUpdateBy(AccountantConstants.DEFAULT_USER);
        tAmsGlinsbal.setCreateTime(LocalDateTime.now());
        tAmsGlinsbal.setVersionNumber(1L);

        logger.info("Install order sum completed: orderId={}, termPostFeeAmt={}", 
                   installOrder.getOrderId(), tAmsGlinsbal.getTermPostFeeAmt());
        return tAmsGlinsbal;
    }

    /**
     * 基于分户余额生成分户余额汇总
     * @param accountBalanceInfo
     * @return
     */
    @Override
    public AccountantGlactbalDTO accountBalanceSum(AccountBalanceInfoDTO accountBalanceInfo) {
        // 账户参数
        AccountManagementInfoDTO accountManagementInfoDTO;
        if (CustAccountBO.isBatch()) {
            accountManagementInfoDTO = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(accountBalanceInfo.getAccountManagementId());
        } else {
            AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.getByOrgNumAndMid(accountBalanceInfo.getOrganizationNumber(), accountBalanceInfo.getAccountManagementId());
            accountManagementInfoDTO = BeanMapping.copy(accountManagementInfo, AccountManagementInfoDTO.class);
        }


        // 机构参数
        logger.info("start to find organization info");
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountBalanceInfo.getOrganizationNumber());
        logger.info("Organization info retrieved: organizationNumber={}, today={}", 
                   accountBalanceInfo.getOrganizationNumber(), organizationInfo.getToday());
        // 分户汇总余额表
        AccountantGlactbalDTO tAmsGlactbal = new AccountantGlactbalDTO();
        tAmsGlactbal.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        tAmsGlactbal.setAccountManagementId(accountManagementInfoDTO.getAccountManagementId());
        tAmsGlactbal.setFinanceStatus(accountManagementInfoDTO.getFinanceStatus());
        tAmsGlactbal.setTransactionTypeCode(accountBalanceInfo.getTransactionTypeCode());
        tAmsGlactbal.setOrganizationNumber(accountManagementInfoDTO.getOrganizationNumber());
        tAmsGlactbal.setCurrency(accountManagementInfoDTO.getCurrency());
        tAmsGlactbal.setBalance(accountBalanceInfo.getBalance());
        tAmsGlactbal.setProcDate(organizationInfo.getToday());
        tAmsGlactbal.setAbsStatus(accountBalanceInfo.getAbsStatus());
        tAmsGlactbal.setAbsProductCode(accountBalanceInfo.getAbsProductCode());
        tAmsGlactbal.setInterestIndicator(accountBalanceInfo.getInterestIndicator());
        tAmsGlactbal.setCreateTime(LocalDateTime.now());
        tAmsGlactbal.setUpdateTime(LocalDateTime.now());
        tAmsGlactbal.setUpdateBy(AccountantConstants.DEFAULT_USER);
        tAmsGlactbal.setVersionNumber(1L);

        return tAmsGlactbal;
    }


}