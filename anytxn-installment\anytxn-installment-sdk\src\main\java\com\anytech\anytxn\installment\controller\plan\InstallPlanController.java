package com.anytech.anytxn.installment.controller.plan;

import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分期计划查询
 *
 * <AUTHOR>
 * @date 2019/9/2
 */
@RestController
@Api(tags = "分期计划")
public class InstallPlanController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallPlanController.class);

    @Autowired
    private IInstallPlanService installPlanService;

    @ApiOperation(value = "根据订单编号和分期期数查询分期计划表")
    @GetMapping(value = "/install/istallplan/orderId/{orderId}/term/{term}")
    public AnyTxnHttpResponse<InstallPlanDTO> getPlanById(@PathVariable String orderId, @PathVariable Integer term) {
        logger.info("Starting get plan by id: orderId={}, term={}", orderId, term);
        InstallPlanDTO installPlanDTO = installPlanService.findByIdAndTerm(orderId, term);
        logger.info("Get plan by id completed successfully: orderId={}, term={}, found={}", orderId, term, installPlanDTO != null);
        return AnyTxnHttpResponse.success(installPlanDTO);
    }

    @ApiOperation(value = "根据订单编号查询分期计划表")
    @GetMapping(value = "/install/istallplan/orderId/{orderId}")
    public AnyTxnHttpResponse<List<InstallPlanDTO>> getPlanByOptions(@PathVariable(value = "orderId") String orderId) {
        logger.info("Starting get plan by options: orderId={}", orderId);
        List<InstallPlanDTO> installPlanDtos = installPlanService.planByOrderId(orderId);
        logger.info("Get plan by options completed successfully: orderId={}, resultCount={}",
                   orderId, installPlanDtos != null ? installPlanDtos.size() : 0);
        return AnyTxnHttpResponse.success(installPlanDtos);
    }
}
